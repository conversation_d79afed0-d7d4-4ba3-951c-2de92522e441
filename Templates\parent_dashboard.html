<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parent Dashboard - BusTrack</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
        }

        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .navbar-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .navbar-user {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            text-align: right;
        }

        .user-name {
            font-weight: 600;
            font-size: 1rem;
        }

        .user-role {
            font-size: 0.85rem;
            opacity: 0.9;
        }

        .nav-links {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: background-color 0.3s ease;
        }

        .nav-links a:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .welcome-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            text-align: center;
        }

        .welcome-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .welcome-subtitle {
            color: #666;
            font-size: 1.1rem;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-right: 1rem;
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
        }

        .children-list {
            list-style: none;
            padding: 0;
        }

        .child-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
            margin-bottom: 8px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .child-name {
            font-weight: 500;
            color: #333;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-online {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
        }

        .status-offline {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
        }

        .map-container {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }

        .map-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .map-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
        }

        .refresh-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        #map {
            height: 400px;
            width: 100%;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .quick-actions h3 {
            margin-bottom: 1rem;
            color: #333;
        }

        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #5a6268;
            box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
        }

        @media (max-width: 768px) {
            .navbar-content {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .container {
                padding: 1rem;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }

            .map-header {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="navbar-content">
            <div class="navbar-brand">
                <i class="fas fa-bus"></i>
                BusTrack
            </div>
            <div class="navbar-user">
                <div class="nav-links">
                    <a href="{% url 'profile' %}"><i class="fas fa-user"></i> Profile</a>
                    <a href="{% url 'signout' %}"><i class="fas fa-sign-out-alt"></i> Logout</a>
                </div>
                <div class="user-info">
                    <div class="user-name">{{ user.get_full_name|default:user.username }}</div>
                    <div class="user-role">Parent</div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="welcome-section">
            <h1 class="welcome-title">Welcome back, {{ user.first_name|default:user.username }}!</h1>
            <p class="welcome-subtitle">Monitor your children's bus location and stay connected</p>
        </div>

        <div class="dashboard-grid">
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <i class="fas fa-children"></i>
                    </div>
                    <div>
                        <h3 class="card-title">My Children</h3>
                    </div>
                </div>
                <ul class="children-list">
                    {% for child_location in children_locations %}
                        <li class="child-item">
                            <span class="child-name">{{ child_location.user.get_full_name|default:child_location.user.username }}</span>
                            {% if child_location.location.is_online %}
                                <span class="status-indicator status-online">
                                    <i class="fas fa-circle"></i>
                                    Online
                                </span>
                            {% else %}
                                <span class="status-indicator status-offline">
                                    <i class="fas fa-circle"></i>
                                    Offline
                                </span>
                            {% endif %}
                        </li>
                    {% empty %}
                        <li class="child-item">
                            <span style="color: #666; font-style: italic;">No children registered</span>
                        </li>
                    {% endfor %}
                </ul>
            </div>

            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                        <i class="fas fa-bus"></i>
                    </div>
                    <div>
                        <h3 class="card-title">Bus Status</h3>
                    </div>
                </div>
                {% if driver_location %}
                    <div class="status-indicator status-online">
                        <i class="fas fa-circle"></i>
                        Bus is online
                    </div>
                    <p style="margin-top: 10px; color: #666; font-size: 0.9rem;">
                        Driver: {{ driver_location.user.get_full_name|default:driver_location.user.username }}
                    </p>
                {% else %}
                    <div class="status-indicator status-offline">
                        <i class="fas fa-circle"></i>
                        Bus location unavailable
                    </div>
                {% endif %}
            </div>
        </div>

        <div class="map-container">
            <div class="map-header">
                <h3 class="map-title">
                    <i class="fas fa-map-marker-alt"></i>
                    Live Location Tracking
                </h3>
                <button class="refresh-btn" onclick="updateMarkers()">
                    <i class="fas fa-sync-alt"></i>
                    Refresh
                </button>
            </div>
            <div id="map"></div>
        </div>

        <div class="quick-actions">
            <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
            <div class="action-buttons">
                <a href="{% url 'profile' %}" class="btn btn-secondary">
                    <i class="fas fa-user-edit"></i>
                    Edit Profile
                </a>
                <button class="btn btn-secondary" onclick="centerMapOnBus()">
                    <i class="fas fa-crosshairs"></i>
                    Center on Bus
                </button>
                <button class="btn btn-secondary">
                    <i class="fas fa-bell"></i>
                    Notifications
                </button>
            </div>
        </div>
    </div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
    <script>
        var map = L.map('map').setView([0, 0], 2);
        var markers = {};
        var busMarker = null;

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 19,
            attribution: '© OpenStreetMap'
        }).addTo(map);

        function updateMarkers() {
            fetch("{% url 'get_locations' %}")
                .then(response => response.json())
                .then(data => {
                    // Clear existing markers
                    Object.values(markers).forEach(marker => map.removeLayer(marker));
                    markers = {};

                    if (busMarker) {
                        map.removeLayer(busMarker);
                        busMarker = null;
                    }

                    data.locations.forEach(loc => {
                        if (loc.latitude && loc.longitude) {
                            var latlng = [loc.latitude, loc.longitude];

                            if (loc.role === 'driver') {
                                // Bus/Driver marker
                                busMarker = L.marker(latlng, {
                                    icon: L.icon({
                                        iconUrl: 'data:image/svg+xml;base64,' + btoa(`
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#667eea" width="32" height="32">
                                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                            </svg>
                                        `),
                                        iconSize: [32, 32],
                                        iconAnchor: [16, 16]
                                    })
                                }).addTo(map);

                                busMarker.bindPopup(`
                                    <div style="text-align: center;">
                                        <h4>🚌 School Bus</h4>
                                        <p><strong>Driver:</strong> ${loc.first_name} ${loc.last_name}</p>
                                        <p><small>Last updated: ${new Date(loc.last_updated).toLocaleTimeString()}</small></p>
                                    </div>
                                `);

                                // Center map on bus location
                                map.setView(latlng, 13);
                            } else if (loc.role === 'student') {
                                // Student marker
                                var key = loc.id + '-student';
                                markers[key] = L.marker(latlng, {
                                    icon: L.icon({
                                        iconUrl: 'data:image/svg+xml;base64,' + btoa(`
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#28a745" width="24" height="24">
                                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                                            </svg>
                                        `),
                                        iconSize: [24, 24],
                                        iconAnchor: [12, 12]
                                    })
                                }).addTo(map);

                                markers[key].bindPopup(`
                                    <div style="text-align: center;">
                                        <h4>👨‍🎓 Student</h4>
                                        <p><strong>${loc.first_name} ${loc.last_name}</strong></p>
                                        <p><small>Last updated: ${new Date(loc.last_updated).toLocaleTimeString()}</small></p>
                                    </div>
                                `);
                            }
                        }
                    });
                })
                .catch(error => {
                    console.error('Error fetching locations:', error);
                });
        }

        function centerMapOnBus() {
            if (busMarker) {
                map.setView(busMarker.getLatLng(), 15);
                busMarker.openPopup();
            } else {
                alert('Bus location not available');
            }
        }

        // Auto-refresh every 30 seconds
        setInterval(updateMarkers, 30000);

        // Initial load
        updateMarkers();
    </script>
</body>
</html>
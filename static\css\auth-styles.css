/**
 * Authentication Styles for BusTrack Application
 * Consistent styling for login, signup, and profile forms
 */

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Container styles */
.auth-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 450px;
    padding: 40px;
    position: relative;
    overflow: hidden;
}

.auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

/* Logo and branding */
.logo-section {
    text-align: center;
    margin-bottom: 30px;
}

.logo {
    font-size: 2.5rem;
    color: #667eea;
    margin-bottom: 10px;
}

.app-name {
    font-size: 1.8rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.app-subtitle {
    color: #666;
    font-size: 0.9rem;
    font-weight: 400;
}

/* Form styles */
.form-row {
    display: flex;
    gap: 15px;
}

.form-row .form-group {
    flex: 1;
}

.form-group {
    margin-bottom: 20px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
    font-size: 0.9rem;
}

.input-wrapper {
    position: relative;
}

.form-control {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 15px;
    transition: all 0.3s ease;
    background: #fff;
    font-family: inherit;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control.is-invalid {
    border-color: #e74c3c;
}

.input-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 1rem;
    z-index: 1;
}

/* Password strength indicator */
.password-strength {
    margin-top: 8px;
    font-size: 0.8rem;
}

.strength-bar {
    height: 4px;
    background: #e1e5e9;
    border-radius: 2px;
    margin: 5px 0;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-weak { 
    background: #e74c3c; 
    width: 25%; 
}

.strength-fair { 
    background: #f39c12; 
    width: 50%; 
}

.strength-good { 
    background: #f1c40f; 
    width: 75%; 
}

.strength-strong { 
    background: #27ae60; 
    width: 100%; 
}

/* Error and success messages */
.error-message {
    color: #e74c3c;
    font-size: 0.8rem;
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.success-message {
    color: #27ae60;
    font-size: 0.85rem;
    margin-bottom: 20px;
    padding: 12px;
    background: rgba(39, 174, 96, 0.1);
    border-radius: 8px;
    border-left: 4px solid #27ae60;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    text-align: center;
    font-family: inherit;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
}

.btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.submit-btn {
    width: 100%;
    padding: 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.submit-btn:active {
    transform: translateY(0);
}

/* Loading spinner */
.loading-spinner {
    display: none;
    width: 20px;
    height: 20px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Links and navigation */
.form-links {
    text-align: center;
    margin-top: 25px;
}

.form-links a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.form-links a:hover {
    color: #764ba2;
    text-decoration: underline;
}

.divider {
    margin: 20px 0;
    text-align: center;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e1e5e9;
}

.divider span {
    background: rgba(255, 255, 255, 0.95);
    padding: 0 15px;
    color: #666;
    font-size: 0.85rem;
}

/* Role information */
.role-info {
    font-size: 0.8rem;
    color: #666;
    margin-top: 5px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #667eea;
}

/* Responsive design */
@media (max-width: 600px) {
    .auth-container {
        padding: 30px 25px;
        margin: 10px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .app-name {
        font-size: 1.5rem;
    }
    
    .form-control {
        padding: 12px 15px 12px 40px;
    }
}

/* Dashboard card styles */
.card-icon-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.card-icon-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.card-icon-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
}

/* Status indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.status-online {
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
}

.status-offline {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.status-pending {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Sent - BusTrack</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .success-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .success-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #27ae60, #2ecc71);
        }

        .success-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            font-size: 2.5rem;
            color: white;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .success-title {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
        }

        .success-subtitle {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .info-box {
            background: rgba(39, 174, 96, 0.1);
            border-left: 4px solid #27ae60;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 8px;
            text-align: left;
        }

        .info-box h3 {
            color: #27ae60;
            font-size: 1.1rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-box p {
            color: #333;
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 10px;
        }

        .info-box ul {
            color: #333;
            font-size: 0.9rem;
            line-height: 1.5;
            margin-left: 20px;
        }

        .info-box li {
            margin-bottom: 5px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 0 10px 10px 0;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #5a6268;
            box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
        }

        .action-buttons {
            margin-top: 30px;
        }

        .resend-info {
            margin-top: 30px;
            padding: 15px;
            background: rgba(255, 193, 7, 0.1);
            border-radius: 8px;
            border-left: 4px solid #ffc107;
        }

        .resend-info p {
            color: #333;
            font-size: 0.85rem;
            margin: 0;
        }

        @media (max-width: 480px) {
            .success-container {
                padding: 30px 25px;
                margin: 10px;
            }
            
            .success-title {
                font-size: 1.5rem;
            }
            
            .action-buttons {
                display: flex;
                flex-direction: column;
                gap: 10px;
            }
            
            .btn {
                margin: 0;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-icon">
            <i class="fas fa-envelope-open"></i>
        </div>
        
        <h1 class="success-title">Check Your Email</h1>
        <p class="success-subtitle">
            We've sent a password reset link to your email address.
        </p>

        <div class="info-box">
            <h3>
                <i class="fas fa-info-circle"></i>
                What's Next?
            </h3>
            <p>Follow these steps to reset your password:</p>
            <ul>
                <li>Check your email inbox (and spam folder)</li>
                <li>Click the reset link in the email</li>
                <li>Create a new secure password</li>
                <li>Sign in with your new password</li>
            </ul>
        </div>

        <div class="action-buttons">
            <a href="{% url 'signin' %}" class="btn">
                <i class="fas fa-sign-in-alt"></i>
                Back to Sign In
            </a>
            <a href="{% url 'home' %}" class="btn btn-secondary">
                <i class="fas fa-home"></i>
                Go Home
            </a>
        </div>

        <div class="resend-info">
            <p>
                <i class="fas fa-clock" style="color: #ffc107; margin-right: 8px;"></i>
                Didn't receive the email? Check your spam folder or 
                <a href="{% url 'password_reset' %}" style="color: #667eea; text-decoration: none; font-weight: 500;">
                    try again
                </a>.
            </p>
        </div>
    </div>

    <script>
        // Auto-refresh page after 5 minutes to allow user to try again
        setTimeout(function() {
            if (confirm('Would you like to try sending the reset email again?')) {
                window.location.href = '{% url "password_reset" %}';
            }
        }, 300000); // 5 minutes
    </script>
</body>
</html>

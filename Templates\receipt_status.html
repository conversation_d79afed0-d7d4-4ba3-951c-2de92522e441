<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt Status - BusTrack</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
        }

        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .navbar-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: background-color 0.3s ease;
            margin-left: 10px;
        }

        .nav-links a:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            text-align: center;
        }

        .page-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: #666;
            font-size: 1.1rem;
        }

        .receipts-container {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .receipts-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }

        .receipts-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .receipts-grid {
            display: grid;
            gap: 1.5rem;
        }

        .receipt-card {
            border: 1px solid #e1e5e9;
            border-radius: 12px;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .receipt-card:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .receipt-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .receipt-amount {
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
        }

        .status-approved {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .status-rejected {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        .receipt-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 4px;
            text-transform: uppercase;
            font-weight: 500;
        }

        .detail-value {
            font-size: 0.9rem;
            color: #333;
            font-weight: 500;
        }

        .receipt-image {
            margin-top: 1rem;
            text-align: center;
        }

        .receipt-image img {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
        }

        .admin-notes {
            margin-top: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .admin-notes h4 {
            color: #667eea;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .admin-notes p {
            color: #333;
            font-size: 0.85rem;
            line-height: 1.4;
            margin: 0;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
        }

        .empty-state i {
            font-size: 4rem;
            color: #e1e5e9;
            margin-bottom: 1rem;
        }

        .empty-state h3 {
            margin-bottom: 1rem;
            color: #333;
        }

        .empty-state p {
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .receipts-header {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            .receipt-details {
                grid-template-columns: 1fr;
            }

            .receipt-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="navbar-content">
            <div class="navbar-brand">
                <i class="fas fa-bus"></i>
                BusTrack
            </div>
            <div class="nav-links">
                <a href="{% url 'student_dashboard' %}"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="{% url 'submit_receipt' %}"><i class="fas fa-plus"></i> Submit Receipt</a>
                <a href="{% url 'signout' %}"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="page-header">
            <h1 class="page-title">My Payment Receipts</h1>
            <p class="page-subtitle">Track the status of your submitted payment receipts</p>
        </div>

        <div class="receipts-container">
            <div class="receipts-header">
                <h3 class="receipts-title">Submitted Receipts</h3>
                <a href="{% url 'submit_receipt' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    Submit New Receipt
                </a>
            </div>

            {% if receipts %}
                <div class="receipts-grid">
                    {% for receipt in receipts %}
                        <div class="receipt-card">
                            <div class="receipt-header">
                                <div class="receipt-amount">${{ receipt.amount }}</div>
                                <div class="status-badge status-{{ receipt.status }}">
                                    {% if receipt.status == 'pending' %}
                                        <i class="fas fa-clock"></i> Pending
                                    {% elif receipt.status == 'approved' %}
                                        <i class="fas fa-check"></i> Approved
                                    {% else %}
                                        <i class="fas fa-times"></i> Rejected
                                    {% endif %}
                                </div>
                            </div>

                            <div class="receipt-details">
                                <div class="detail-item">
                                    <div class="detail-label">Payment Date</div>
                                    <div class="detail-value">{{ receipt.payment_date|date:"M d, Y" }}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Submitted</div>
                                    <div class="detail-value">{{ receipt.submitted_at|date:"M d, Y H:i" }}</div>
                                </div>
                                {% if receipt.verification_date %}
                                    <div class="detail-item">
                                        <div class="detail-label">Verified</div>
                                        <div class="detail-value">{{ receipt.verification_date|date:"M d, Y H:i" }}</div>
                                    </div>
                                {% endif %}
                                {% if receipt.verified_by %}
                                    <div class="detail-item">
                                        <div class="detail-label">Verified By</div>
                                        <div class="detail-value">{{ receipt.verified_by.get_full_name|default:receipt.verified_by.username }}</div>
                                    </div>
                                {% endif %}
                            </div>

                            {% if receipt.description %}
                                <div class="detail-item">
                                    <div class="detail-label">Description</div>
                                    <div class="detail-value">{{ receipt.description }}</div>
                                </div>
                            {% endif %}

                            {% if receipt.receipt_image %}
                                <div class="receipt-image">
                                    <img src="{{ receipt.receipt_image.url }}" alt="Receipt" onclick="openImageModal('{{ receipt.receipt_image.url }}')">
                                </div>
                            {% endif %}

                            {% if receipt.admin_notes %}
                                <div class="admin-notes">
                                    <h4><i class="fas fa-comment"></i> Admin Notes</h4>
                                    <p>{{ receipt.admin_notes }}</p>
                                </div>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-receipt"></i>
                    <h3>No Receipts Submitted</h3>
                    <p>You haven't submitted any payment receipts yet. Submit your first receipt to get verified for bus access.</p>
                    <a href="{% url 'submit_receipt' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Submit Your First Receipt
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <script>
        function openImageModal(imageUrl) {
            // Create modal overlay
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                cursor: pointer;
            `;

            // Create image
            const img = document.createElement('img');
            img.src = imageUrl;
            img.style.cssText = `
                max-width: 90%;
                max-height: 90%;
                border-radius: 8px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            `;

            modal.appendChild(img);
            document.body.appendChild(modal);

            // Close modal on click
            modal.addEventListener('click', function() {
                document.body.removeChild(modal);
            });
        }
    </script>
</body>
</html>

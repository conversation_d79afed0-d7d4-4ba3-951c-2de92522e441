<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - BusTrack</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/auth-styles.css' %}">
    <script src="{% static 'js/form-validation.js' %}" defer></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .signup-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            padding: 40px;
            position: relative;
            overflow: hidden;
            max-height: 90vh;
            overflow-y: auto;
        }

        .signup-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .logo-section {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 10px;
        }

        .app-name {
            font-size: 1.8rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }

        .app-subtitle {
            color: #666;
            font-size: 0.9rem;
            font-weight: 400;
        }

        .form-row {
            display: flex;
            gap: 15px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .form-group {
            margin-bottom: 20px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .input-wrapper {
            position: relative;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px 12px 45px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 15px;
            transition: all 0.3s ease;
            background: #fff;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 1rem;
        }

        .password-strength {
            margin-top: 8px;
            font-size: 0.8rem;
        }

        .strength-bar {
            height: 4px;
            background: #e1e5e9;
            border-radius: 2px;
            margin: 5px 0;
            overflow: hidden;
        }

        .strength-fill {
            height: 100%;
            width: 0%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }

        .strength-weak { background: #e74c3c; width: 25%; }
        .strength-fair { background: #f39c12; width: 50%; }
        .strength-good { background: #f1c40f; width: 75%; }
        .strength-strong { background: #27ae60; width: 100%; }

        .error-message {
            color: #e74c3c;
            font-size: 0.8rem;
            margin-top: 5px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .success-message {
            color: #27ae60;
            font-size: 0.85rem;
            margin-bottom: 20px;
            padding: 12px;
            background: rgba(39, 174, 96, 0.1);
            border-radius: 8px;
            border-left: 4px solid #27ae60;
        }

        .submit-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .submit-btn:active {
            transform: translateY(0);
        }

        .submit-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .form-links {
            text-align: center;
            margin-top: 25px;
        }

        .form-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .form-links a:hover {
            color: #764ba2;
            text-decoration: underline;
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .role-info {
            font-size: 0.8rem;
            color: #666;
            margin-top: 5px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 3px solid #667eea;
        }

        @media (max-width: 600px) {
            .signup-container {
                padding: 30px 25px;
                margin: 10px;
            }

            .form-row {
                flex-direction: column;
                gap: 0;
            }

            .app-name {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="signup-container">
        <div class="logo-section">
            <div class="logo">
                <i class="fas fa-bus"></i>
            </div>
            <h1 class="app-name">BusTrack</h1>
            <p class="app-subtitle">Create Your Account</p>
        </div>

        {% if messages %}
            {% for message in messages %}
                <div class="{% if message.tags == 'error' %}error-message{% else %}success-message{% endif %}">
                    <i class="fas fa-{% if message.tags == 'error' %}exclamation-circle{% else %}check-circle{% endif %}"></i>
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}

        <form method="post" id="signupForm" novalidate>
            {% csrf_token %}

            <div class="form-row">
                <div class="form-group">
                    <label for="{{ form.first_name.id_for_label }}">First Name</label>
                    <div class="input-wrapper">
                        <i class="fas fa-user input-icon"></i>
                        {{ form.first_name }}
                    </div>
                    {% if form.first_name.errors %}
                        <div class="error-message">
                            <i class="fas fa-exclamation-circle"></i>
                            {{ form.first_name.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.last_name.id_for_label }}">Last Name</label>
                    <div class="input-wrapper">
                        <i class="fas fa-user input-icon"></i>
                        {{ form.last_name }}
                    </div>
                    {% if form.last_name.errors %}
                        <div class="error-message">
                            <i class="fas fa-exclamation-circle"></i>
                            {{ form.last_name.errors.0 }}
                        </div>
                    {% endif %}
                </div>
            </div>

            <div class="form-group">
                <label for="{{ form.username.id_for_label }}">Username</label>
                <div class="input-wrapper">
                    <i class="fas fa-at input-icon"></i>
                    {{ form.username }}
                </div>
                {% if form.username.errors %}
                    <div class="error-message">
                        <i class="fas fa-exclamation-circle"></i>
                        {{ form.username.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="{{ form.email.id_for_label }}">Email Address</label>
                <div class="input-wrapper">
                    <i class="fas fa-envelope input-icon"></i>
                    {{ form.email }}
                </div>
                {% if form.email.errors %}
                    <div class="error-message">
                        <i class="fas fa-exclamation-circle"></i>
                        {{ form.email.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="{{ form.phone_number.id_for_label }}">Phone Number (Optional)</label>
                <div class="input-wrapper">
                    <i class="fas fa-phone input-icon"></i>
                    {{ form.phone_number }}
                </div>
                {% if form.phone_number.errors %}
                    <div class="error-message">
                        <i class="fas fa-exclamation-circle"></i>
                        {{ form.phone_number.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="{{ form.role.id_for_label }}">Role</label>
                <div class="input-wrapper">
                    <i class="fas fa-user-tag input-icon"></i>
                    {{ form.role }}
                </div>
                <div class="role-info" id="roleInfo">
                    Select your role in the school bus system
                </div>
                {% if form.role.errors %}
                    <div class="error-message">
                        <i class="fas fa-exclamation-circle"></i>
                        {{ form.role.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="{{ form.password1.id_for_label }}">Password</label>
                <div class="input-wrapper">
                    <i class="fas fa-lock input-icon"></i>
                    {{ form.password1 }}
                </div>
                <div class="password-strength" id="passwordStrength" style="display: none;">
                    <div class="strength-bar">
                        <div class="strength-fill" id="strengthFill"></div>
                    </div>
                    <span id="strengthText">Password strength</span>
                </div>
                {% if form.password1.errors %}
                    <div class="error-message">
                        <i class="fas fa-exclamation-circle"></i>
                        {{ form.password1.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="{{ form.password2.id_for_label }}">Confirm Password</label>
                <div class="input-wrapper">
                    <i class="fas fa-lock input-icon"></i>
                    {{ form.password2 }}
                </div>
                {% if form.password2.errors %}
                    <div class="error-message">
                        <i class="fas fa-exclamation-circle"></i>
                        {{ form.password2.errors.0 }}
                    </div>
                {% endif %}
            </div>

            <button type="submit" class="submit-btn" id="submitBtn">
                <span class="loading-spinner" id="loadingSpinner"></span>
                <span id="btnText">Create Account</span>
            </button>
        </form>

        <div class="form-links">
            <p>Already have an account? <a href="{% url 'signin' %}">Sign In</a></p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('signupForm');
            const submitBtn = document.getElementById('submitBtn');
            const loadingSpinner = document.getElementById('loadingSpinner');
            const btnText = document.getElementById('btnText');
            const passwordField = document.getElementById('id_password1');
            const confirmPasswordField = document.getElementById('id_password2');
            const roleField = document.getElementById('id_role');
            const roleInfo = document.getElementById('roleInfo');

            // Role descriptions (admin removed from regular registration)
            const roleDescriptions = {
                'student': 'Students can track their bus location and upload payment receipts',
                'parent': 'Parents can monitor their children\'s bus location and manage accounts',
                'driver': 'Drivers can update bus location and view assigned students'
            };

            // Update role info when role changes
            roleField.addEventListener('change', function() {
                const selectedRole = this.value;
                roleInfo.textContent = roleDescriptions[selectedRole] || 'Select your role in the school bus system';
            });

            // Password strength checker
            passwordField.addEventListener('input', function() {
                checkPasswordStrength(this.value);
            });

            function checkPasswordStrength(password) {
                const strengthIndicator = document.getElementById('passwordStrength');
                const strengthFill = document.getElementById('strengthFill');
                const strengthText = document.getElementById('strengthText');

                if (password.length === 0) {
                    strengthIndicator.style.display = 'none';
                    return;
                }

                strengthIndicator.style.display = 'block';

                let score = 0;
                let feedback = [];

                // Length check
                if (password.length >= 8) score++;
                else feedback.push('at least 8 characters');

                // Uppercase check
                if (/[A-Z]/.test(password)) score++;
                else feedback.push('an uppercase letter');

                // Lowercase check
                if (/[a-z]/.test(password)) score++;
                else feedback.push('a lowercase letter');

                // Number check
                if (/\d/.test(password)) score++;
                else feedback.push('a number');

                // Special character check
                if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score++;

                // Update strength indicator
                strengthFill.className = 'strength-fill';
                if (score <= 1) {
                    strengthFill.classList.add('strength-weak');
                    strengthText.textContent = 'Weak - Add ' + feedback.slice(0, 2).join(' and ');
                } else if (score <= 2) {
                    strengthFill.classList.add('strength-fair');
                    strengthText.textContent = 'Fair - Add ' + feedback.slice(0, 1).join(' and ');
                } else if (score <= 3) {
                    strengthFill.classList.add('strength-good');
                    strengthText.textContent = 'Good - Almost there!';
                } else {
                    strengthFill.classList.add('strength-strong');
                    strengthText.textContent = 'Strong password!';
                }
            }

            // Form submission
            form.addEventListener('submit', function(e) {
                submitBtn.disabled = true;
                loadingSpinner.style.display = 'inline-block';
                btnText.textContent = 'Creating Account...';
            });

            // Real-time validation
            const inputs = form.querySelectorAll('.form-control');
            inputs.forEach(input => {
                input.addEventListener('blur', validateField);
                input.addEventListener('input', clearErrors);
            });

            function validateField(e) {
                const field = e.target;
                const value = field.value.trim();

                clearFieldErrors(field);

                // Email validation
                if (field.name === 'email' && value) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(value)) {
                        showFieldError(field, 'Please enter a valid email address');
                    }
                }

                // Username validation
                if (field.name === 'username' && value) {
                    if (value.length < 3) {
                        showFieldError(field, 'Username must be at least 3 characters');
                    } else if (!/^[a-zA-Z0-9_]+$/.test(value)) {
                        showFieldError(field, 'Username can only contain letters, numbers, and underscores');
                    }
                }

                // Password confirmation
                if (field.name === 'password2' && value) {
                    if (value !== passwordField.value) {
                        showFieldError(field, 'Passwords do not match');
                    }
                }
            }

            function clearErrors(e) {
                clearFieldErrors(e.target);
            }

            function showFieldError(field, message) {
                const formGroup = field.closest('.form-group');
                const existingError = formGroup.querySelector('.error-message');

                if (!existingError) {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'error-message';
                    errorDiv.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
                    formGroup.appendChild(errorDiv);
                }

                field.style.borderColor = '#e74c3c';
            }

            function clearFieldErrors(field) {
                const formGroup = field.closest('.form-group');
                const errorMessage = formGroup.querySelector('.error-message');
                if (errorMessage && !errorMessage.textContent.includes('{{ form')) {
                    errorMessage.remove();
                }
                field.style.borderColor = '#e1e5e9';
            }
        });
    </script>
</body>
</html>
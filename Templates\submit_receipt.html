<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Submit Payment Receipt - BusTrack</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
        }

        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .navbar-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: background-color 0.3s ease;
            margin-left: 10px;
        }

        .nav-links a:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }

        .receipt-form-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .form-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .form-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .form-subtitle {
            color: #666;
            font-size: 1.1rem;
        }

        .info-box {
            background: rgba(102, 126, 234, 0.1);
            border-left: 4px solid #667eea;
            padding: 15px;
            margin-bottom: 25px;
            border-radius: 8px;
        }

        .info-box h4 {
            color: #667eea;
            font-size: 1rem;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-box p {
            color: #333;
            font-size: 0.9rem;
            line-height: 1.5;
            margin: 0;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 15px;
            transition: all 0.3s ease;
            background: #fff;
            font-family: inherit;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .file-upload-area {
            border: 2px dashed #e1e5e9;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-upload-area:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .file-upload-area.dragover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .upload-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .upload-text {
            color: #333;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .upload-subtext {
            color: #666;
            font-size: 0.9rem;
        }

        .file-preview {
            margin-top: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            display: none;
        }

        .file-preview img {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .error-message {
            color: #e74c3c;
            font-size: 0.85rem;
            margin-top: 5px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .success-message {
            color: #27ae60;
            font-size: 0.85rem;
            margin-bottom: 20px;
            padding: 12px;
            background: rgba(39, 174, 96, 0.1);
            border-radius: 8px;
            border-left: 4px solid #27ae60;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            font-family: inherit;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .form-actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .receipt-form-card {
                padding: 1.5rem;
            }

            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="navbar-content">
            <div class="navbar-brand">
                <i class="fas fa-bus"></i>
                BusTrack
            </div>
            <div class="nav-links">
                <a href="{% url 'student_dashboard' %}"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="{% url 'receipt_status' %}"><i class="fas fa-list"></i> My Receipts</a>
                <a href="{% url 'signout' %}"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="receipt-form-card">
            <div class="form-header">
                <h1 class="form-title">Submit Payment Receipt</h1>
                <p class="form-subtitle">Upload your payment receipt for verification</p>
            </div>

            <div class="info-box">
                <h4>
                    <i class="fas fa-info-circle"></i>
                    Important Information
                </h4>
                <p>
                    Please upload a clear image of your payment receipt. Our administrators will verify your payment 
                    and approve your access to the bus service. Make sure the receipt shows your name and payment amount clearly.
                </p>
            </div>

            {% if messages %}
                {% for message in messages %}
                    <div class="{% if message.tags == 'error' %}error-message{% else %}success-message{% endif %}">
                        <i class="fas fa-{% if message.tags == 'error' %}exclamation-circle{% else %}check-circle{% endif %}"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            <form method="post" enctype="multipart/form-data" id="receiptForm">
                {% csrf_token %}
                
                <div class="form-group">
                    <label for="{{ form.receipt_image.id_for_label }}">Receipt Image *</label>
                    <div class="file-upload-area" id="fileUploadArea">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">Click to upload or drag and drop</div>
                        <div class="upload-subtext">PNG, JPG, JPEG up to 5MB</div>
                        {{ form.receipt_image }}
                    </div>
                    <div class="file-preview" id="filePreview">
                        <img id="previewImage" src="" alt="Receipt preview">
                    </div>
                    {% if form.receipt_image.errors %}
                        <div class="error-message">
                            <i class="fas fa-exclamation-circle"></i>
                            {{ form.receipt_image.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.amount.id_for_label }}">Payment Amount *</label>
                    {{ form.amount }}
                    {% if form.amount.errors %}
                        <div class="error-message">
                            <i class="fas fa-exclamation-circle"></i>
                            {{ form.amount.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.payment_date.id_for_label }}">Payment Date *</label>
                    {{ form.payment_date }}
                    {% if form.payment_date.errors %}
                        <div class="error-message">
                            <i class="fas fa-exclamation-circle"></i>
                            {{ form.payment_date.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.description.id_for_label }}">Description (Optional)</label>
                    {{ form.description }}
                    {% if form.description.errors %}
                        <div class="error-message">
                            <i class="fas fa-exclamation-circle"></i>
                            {{ form.description.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i>
                        Submit Receipt
                    </button>
                    <a href="{% url 'student_dashboard' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        Back to Dashboard
                    </a>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.getElementById('id_receipt_image');
            const uploadArea = document.getElementById('fileUploadArea');
            const filePreview = document.getElementById('filePreview');
            const previewImage = document.getElementById('previewImage');

            // File upload area click
            uploadArea.addEventListener('click', function() {
                fileInput.click();
            });

            // Drag and drop functionality
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    handleFileSelect(files[0]);
                }
            });

            // File input change
            fileInput.addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    handleFileSelect(e.target.files[0]);
                }
            });

            function handleFileSelect(file) {
                if (file && file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        previewImage.src = e.target.result;
                        filePreview.style.display = 'block';
                    };
                    reader.readAsDataURL(file);
                }
            }

            // Set today's date as default
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('id_payment_date').value = today;
        });
    </script>
</body>
</html>

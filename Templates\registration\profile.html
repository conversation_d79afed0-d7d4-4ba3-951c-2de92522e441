<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - BusTrack</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
        }

        .profile-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 30px;
            text-align: center;
            color: white;
        }

        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2.5rem;
        }

        .profile-name {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .profile-role {
            opacity: 0.9;
            font-size: 1rem;
        }

        .profile-body {
            padding: 30px;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 15px;
            transition: all 0.3s ease;
            background: #fff;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        .error-message {
            color: #e74c3c;
            font-size: 0.85rem;
            margin-top: 5px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .success-message {
            color: #27ae60;
            font-size: 0.85rem;
            margin-bottom: 20px;
            padding: 12px;
            background: rgba(39, 174, 96, 0.1);
            border-radius: 8px;
            border-left: 4px solid #27ae60;
        }

        .nav-links {
            text-align: center;
            margin-bottom: 20px;
        }

        .nav-links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 15px;
            font-weight: 500;
        }

        .nav-links a:hover {
            text-decoration: underline;
        }

        @media (max-width: 600px) {
            .form-row {
                flex-direction: column;
                gap: 0;
            }
            
            .btn-group {
                flex-direction: column;
            }
            
            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-links">
            <a href="{% url 'home' %}"><i class="fas fa-home"></i> Home</a>
            {% if user.role == 'student' %}
                <a href="{% url 'student_dashboard' %}"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
            {% elif user.role == 'driver' %}
                <a href="{% url 'driver_dashboard' %}"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
            {% elif user.role == 'parent' %}
                <a href="{% url 'parent_dashboard' %}"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
            {% endif %}
            <a href="{% url 'signout' %}"><i class="fas fa-sign-out-alt"></i> Logout</a>
        </div>

        <div class="profile-card">
            <div class="profile-header">
                <div class="profile-avatar">
                    {% if user.profile_picture %}
                        <img src="{{ user.profile_picture.url }}" alt="Profile Picture" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">
                    {% else %}
                        <i class="fas fa-user"></i>
                    {% endif %}
                </div>
                <div class="profile-name">{{ user.get_full_name|default:user.username }}</div>
                <div class="profile-role">{{ user.get_role_display_name }}</div>
            </div>

            <div class="profile-body">
                {% if messages %}
                    {% for message in messages %}
                        <div class="{% if message.tags == 'error' %}error-message{% else %}success-message{% endif %}">
                            <i class="fas fa-{% if message.tags == 'error' %}exclamation-circle{% else %}check-circle{% endif %}"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.first_name.id_for_label }}">First Name</label>
                            {{ form.first_name }}
                            {% if form.first_name.errors %}
                                <div class="error-message">
                                    <i class="fas fa-exclamation-circle"></i>
                                    {{ form.first_name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.last_name.id_for_label }}">Last Name</label>
                            {{ form.last_name }}
                            {% if form.last_name.errors %}
                                <div class="error-message">
                                    <i class="fas fa-exclamation-circle"></i>
                                    {{ form.last_name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="{{ form.email.id_for_label }}">Email Address</label>
                        {{ form.email }}
                        {% if form.email.errors %}
                            <div class="error-message">
                                <i class="fas fa-exclamation-circle"></i>
                                {{ form.email.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        <label for="{{ form.phone_number.id_for_label }}">Phone Number</label>
                        {{ form.phone_number }}
                        {% if form.phone_number.errors %}
                            <div class="error-message">
                                <i class="fas fa-exclamation-circle"></i>
                                {{ form.phone_number.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        <label for="{{ form.address.id_for_label }}">Address</label>
                        {{ form.address }}
                        {% if form.address.errors %}
                            <div class="error-message">
                                <i class="fas fa-exclamation-circle"></i>
                                {{ form.address.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.emergency_contact.id_for_label }}">Emergency Contact</label>
                            {{ form.emergency_contact }}
                            {% if form.emergency_contact.errors %}
                                <div class="error-message">
                                    <i class="fas fa-exclamation-circle"></i>
                                    {{ form.emergency_contact.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.emergency_phone.id_for_label }}">Emergency Phone</label>
                            {{ form.emergency_phone }}
                            {% if form.emergency_phone.errors %}
                                <div class="error-message">
                                    <i class="fas fa-exclamation-circle"></i>
                                    {{ form.emergency_phone.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="{{ form.profile_picture.id_for_label }}">Profile Picture</label>
                        {{ form.profile_picture }}
                        {% if form.profile_picture.errors %}
                            <div class="error-message">
                                <i class="fas fa-exclamation-circle"></i>
                                {{ form.profile_picture.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="btn-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Profile
                        </button>
                        <a href="{% url 'password_reset' %}" class="btn btn-secondary">
                            <i class="fas fa-key"></i> Change Password
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset - BusTrack</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .reset-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
            padding: 40px;
            position: relative;
            overflow: hidden;
        }

        .reset-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .logo-section {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 10px;
        }

        .app-name {
            font-size: 1.8rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }

        .app-subtitle {
            color: #666;
            font-size: 0.9rem;
            font-weight: 400;
        }

        .reset-info {
            background: rgba(102, 126, 234, 0.1);
            border-left: 4px solid #667eea;
            padding: 15px;
            margin-bottom: 25px;
            border-radius: 8px;
        }

        .reset-info p {
            color: #333;
            font-size: 0.9rem;
            line-height: 1.5;
            margin: 0;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .input-wrapper {
            position: relative;
        }

        .form-control {
            width: 100%;
            padding: 15px 20px 15px 50px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #fff;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 1.1rem;
        }

        .error-message {
            color: #e74c3c;
            font-size: 0.85rem;
            margin-top: 5px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .success-message {
            color: #27ae60;
            font-size: 0.85rem;
            margin-bottom: 20px;
            padding: 12px;
            background: rgba(39, 174, 96, 0.1);
            border-radius: 8px;
            border-left: 4px solid #27ae60;
        }

        .submit-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .submit-btn:active {
            transform: translateY(0);
        }

        .submit-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .form-links {
            text-align: center;
            margin-top: 25px;
        }

        .form-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .form-links a:hover {
            color: #764ba2;
            text-decoration: underline;
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 480px) {
            .reset-container {
                padding: 30px 25px;
                margin: 10px;
            }
            
            .app-name {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="logo-section">
            <div class="logo">
                <i class="fas fa-key"></i>
            </div>
            <h1 class="app-name">Reset Password</h1>
            <p class="app-subtitle">BusTrack - School Bus Tracking</p>
        </div>

        <div class="reset-info">
            <p>
                <i class="fas fa-info-circle" style="color: #667eea; margin-right: 8px;"></i>
                Enter your email address and we'll send you a link to reset your password.
            </p>
        </div>

        {% if messages %}
            {% for message in messages %}
                <div class="{% if message.tags == 'error' %}error-message{% else %}success-message{% endif %}">
                    <i class="fas fa-{% if message.tags == 'error' %}exclamation-circle{% else %}check-circle{% endif %}"></i>
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}

        <form method="post" id="resetForm" novalidate>
            {% csrf_token %}
            
            <div class="form-group">
                <label for="id_email">Email Address</label>
                <div class="input-wrapper">
                    <i class="fas fa-envelope input-icon"></i>
                    <input type="email" name="email" id="id_email" class="form-control" 
                           placeholder="Enter your email address" required>
                </div>
                <div class="error-message" id="emailError" style="display: none;">
                    <i class="fas fa-exclamation-circle"></i>
                    <span id="emailErrorText"></span>
                </div>
            </div>

            <button type="submit" class="submit-btn" id="submitBtn">
                <span class="loading-spinner" id="loadingSpinner"></span>
                <span id="btnText">Send Reset Link</span>
            </button>
        </form>

        <div class="form-links">
            <p>Remember your password? <a href="{% url 'signin' %}">Sign In</a></p>
            <p>Don't have an account? <a href="{% url 'signup' %}">Sign Up</a></p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('resetForm');
            const submitBtn = document.getElementById('submitBtn');
            const loadingSpinner = document.getElementById('loadingSpinner');
            const btnText = document.getElementById('btnText');
            const emailField = document.getElementById('id_email');
            const emailError = document.getElementById('emailError');
            const emailErrorText = document.getElementById('emailErrorText');

            form.addEventListener('submit', function(e) {
                if (!validateEmail()) {
                    e.preventDefault();
                    return false;
                }

                // Show loading state
                submitBtn.disabled = true;
                loadingSpinner.style.display = 'inline-block';
                btnText.textContent = 'Sending...';
            });

            emailField.addEventListener('blur', validateEmail);
            emailField.addEventListener('input', clearEmailError);

            function validateEmail() {
                const email = emailField.value.trim();
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

                clearEmailError();

                if (!email) {
                    showEmailError('Email address is required');
                    return false;
                }

                if (!emailRegex.test(email)) {
                    showEmailError('Please enter a valid email address');
                    return false;
                }

                return true;
            }

            function showEmailError(message) {
                emailErrorText.textContent = message;
                emailError.style.display = 'flex';
                emailField.style.borderColor = '#e74c3c';
            }

            function clearEmailError() {
                emailError.style.display = 'none';
                emailField.style.borderColor = '#e1e5e9';
            }
        });
    </script>
</body>
</html>

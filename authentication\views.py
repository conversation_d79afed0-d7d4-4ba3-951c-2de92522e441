from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth import authenticate, login, logout
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import user_passes_test
from django.core.exceptions import PermissionDenied
from .forms import SignUpForm, CustomLoginForm, UserProfileForm
from .models import User, UserLocation, BusRoute, StudentProfile
import json

def home(request):
    return render(request, 'home.html')

@login_required
def student_dashboard(request):
    # Get driver location if student is assigned to a route
    driver_location = None
    try:
        bus_route = request.user.bus_routes.first()
        if bus_route:
            driver_location = UserLocation.objects.get(user=bus_route.driver)
    except UserLocation.DoesNotExist:
        pass
    
    context = {
        'driver_location': driver_location,
    }
    return render(request, 'student_dashboard.html', context)

@login_required
def driver_dashboard(request):
    # Get all students assigned to this driver's routes
    students_locations = []
    try:
        bus_routes = BusRoute.objects.filter(driver=request.user)
        for route in bus_routes:
            for student in route.students.all():
                try:
                    location = UserLocation.objects.get(user=student)
                    if location.is_online:
                        students_locations.append({
                            'user': student,
                            'location': location
                        })
                except UserLocation.DoesNotExist:
                    pass
    except Exception as e:
        pass
    
    context = {
        'students_locations': students_locations,
    }
    return render(request, 'driver_dashboard.html', context)

@login_required
def parent_dashboard(request):
    # Get children locations and driver location
    children_locations = []
    driver_location = None
    
    try:
        children = StudentProfile.objects.filter(parent=request.user)
        for child_profile in children:
            try:
                location = UserLocation.objects.get(user=child_profile.user)
                children_locations.append({
                    'user': child_profile.user,
                    'location': location
                })
                
                # Get driver location for the child's route
                bus_route = child_profile.user.bus_routes.first()
                if bus_route and not driver_location:
                    driver_location = UserLocation.objects.get(user=bus_route.driver)
            except UserLocation.DoesNotExist:
                pass
    except Exception as e:
        pass
    
    context = {
        'children_locations': children_locations,
        'driver_location': driver_location,
    }
    return render(request, 'parent_dashboard.html', context)

@csrf_exempt
@login_required
def update_location(request):
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            latitude = data.get('latitude')
            longitude = data.get('longitude')
            
            if latitude and longitude:
                location, created = UserLocation.objects.get_or_create(user=request.user)
                location.latitude = latitude
                location.longitude = longitude
                location.is_online = True
                location.save()
                
                return JsonResponse({'status': 'success'})
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)})
    
    return JsonResponse({'status': 'error', 'message': 'Invalid request'})

@login_required
def get_locations(request):
    """API endpoint to get real-time locations based on user role"""
    locations = []
    
    if request.user.role == 'driver':
        # Driver sees all students in their routes
        bus_routes = BusRoute.objects.filter(driver=request.user)
        for route in bus_routes:
            for student in route.students.all():
                try:
                    location = UserLocation.objects.get(user=student, is_online=True)
                    locations.append({
                        'id': student.id,
                        'username': student.username,
                        'first_name': student.first_name,
                        'last_name': student.last_name,
                        'role': student.role,
                        'latitude': float(location.latitude) if location.latitude else None,
                        'longitude': float(location.longitude) if location.longitude else None,
                        'last_updated': location.last_updated.isoformat(),
                    })
                except UserLocation.DoesNotExist:
                    pass
                    
    elif request.user.role == 'student':
        # Student sees their driver
        bus_route = request.user.bus_routes.first()
        if bus_route:
            try:
                location = UserLocation.objects.get(user=bus_route.driver, is_online=True)
                locations.append({
                    'id': bus_route.driver.id,
                    'username': bus_route.driver.username,
                    'first_name': bus_route.driver.first_name,
                    'last_name': bus_route.driver.last_name,
                    'role': bus_route.driver.role,
                    'latitude': float(location.latitude) if location.latitude else None,
                    'longitude': float(location.longitude) if location.longitude else None,
                    'last_updated': location.last_updated.isoformat(),
                })
            except UserLocation.DoesNotExist:
                pass
                
    elif request.user.role == 'parent':
        # Parent sees their children and the driver
        children = StudentProfile.objects.filter(parent=request.user)
        driver_added = False
        
        for child_profile in children:
            # Add child location
            try:
                location = UserLocation.objects.get(user=child_profile.user, is_online=True)
                locations.append({
                    'id': child_profile.user.id,
                    'username': child_profile.user.username,
                    'first_name': child_profile.user.first_name,
                    'last_name': child_profile.user.last_name,
                    'role': child_profile.user.role,
                    'latitude': float(location.latitude) if location.latitude else None,
                    'longitude': float(location.longitude) if location.longitude else None,
                    'last_updated': location.last_updated.isoformat(),
                })
            except UserLocation.DoesNotExist:
                pass
            
            # Add driver location (only once)
            if not driver_added:
                bus_route = child_profile.user.bus_routes.first()
                if bus_route:
                    try:
                        location = UserLocation.objects.get(user=bus_route.driver, is_online=True)
                        locations.append({
                            'id': bus_route.driver.id,
                            'username': bus_route.driver.username,
                            'first_name': bus_route.driver.first_name,
                            'last_name': bus_route.driver.last_name,
                            'role': bus_route.driver.role,
                            'latitude': float(location.latitude) if location.latitude else None,
                            'longitude': float(location.longitude) if location.longitude else None,
                            'last_updated': location.last_updated.isoformat(),
                        })
                        driver_added = True
                    except UserLocation.DoesNotExist:
                        pass
    
    return JsonResponse({'locations': locations})

@csrf_exempt
@login_required
def set_offline(request):
    """Mark user as offline"""
    if request.method == 'POST':
        try:
            location = UserLocation.objects.get(user=request.user)
            location.is_online = False
            location.save()
            return JsonResponse({'status': 'success'})
        except UserLocation.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': 'Location not found'})
    
    return JsonResponse({'status': 'error', 'message': 'Invalid request'})

def get_client_ip(request):
    """Get the client's IP address from the request."""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

def role_required(allowed_roles):
    """Decorator to check if user has required role."""
    def decorator(view_func):
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return redirect('signin')
            if request.user.role not in allowed_roles:
                raise PermissionDenied("You don't have permission to access this page.")
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator

@require_http_methods(["GET", "POST"])
def signup(request):
    if request.user.is_authenticated:
        return redirect_to_dashboard(request.user)

    if request.method == 'POST':
        form = SignUpForm(request.POST)
        if form.is_valid():
            try:
                user = form.save()
                # Store IP address for security tracking
                user.last_login_ip = get_client_ip(request)
                user.save()

                messages.success(request, 'Account created successfully! Please log in to continue.')
                return redirect('signin')
            except Exception as e:
                messages.error(request, 'An error occurred during registration. Please try again.')
        else:
            # Add form errors to messages
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f"{field.title()}: {error}")
    else:
        form = SignUpForm()

    return render(request, 'registration/signup.html', {'form': form})

def redirect_to_dashboard(user):
    """Redirect user to appropriate dashboard based on role."""
    role_dashboard_map = {
        'student': 'student_dashboard',
        'driver': 'driver_dashboard',
        'parent': 'parent_dashboard',
        'admin': 'admin_dashboard',  # We'll create this later
    }
    return redirect(role_dashboard_map.get(user.role, 'home'))

@require_http_methods(["GET", "POST"])
def signin(request):
    if request.user.is_authenticated:
        return redirect_to_dashboard(request.user)

    if request.method == 'POST':
        form = CustomLoginForm(request, data=request.POST)
        if form.is_valid():
            user = form.get_user()

            # Update last login IP
            user.last_login_ip = get_client_ip(request)
            user.save(update_fields=['last_login_ip'])

            login(request, user)

            # Set session timeout (30 minutes of inactivity)
            request.session.set_expiry(1800)

            messages.success(request, f'Welcome back, {user.get_full_name() or user.username}!')

            # Redirect to intended page or dashboard
            next_url = request.GET.get('next')
            if next_url:
                return redirect(next_url)
            return redirect_to_dashboard(user)
        else:
            # Add form errors to messages
            for error in form.non_field_errors():
                messages.error(request, error)
    else:
        form = CustomLoginForm()

    return render(request, 'registration/login.html', {'form': form})

@login_required
def signout(request):
    """Logout view with proper session cleanup."""
    user_name = request.user.get_full_name() or request.user.username
    logout(request)
    messages.success(request, f'Goodbye, {user_name}! You have been logged out successfully.')
    return redirect('home')

@login_required
def profile_view(request):
    """View and edit user profile."""
    if request.method == 'POST':
        form = UserProfileForm(request.POST, request.FILES, instance=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, 'Profile updated successfully!')
            return redirect('profile')
        else:
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f"{field.title()}: {error}")
    else:
        form = UserProfileForm(instance=request.user)

    return render(request, 'registration/profile.html', {'form': form})
def location_tracker(request):
    return render(request, 'location_tracker.html')
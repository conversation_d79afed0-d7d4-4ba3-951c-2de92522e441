<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Dashboard - BusTrack</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
        }

        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .navbar-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .navbar-user {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            text-align: right;
        }

        .user-name {
            font-weight: 600;
            font-size: 1rem;
        }

        .user-role {
            font-size: 0.85rem;
            opacity: 0.9;
        }

        .nav-links {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: background-color 0.3s ease;
        }

        .nav-links a:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .welcome-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            text-align: center;
        }

        .welcome-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .welcome-subtitle {
            color: #666;
            font-size: 1.1rem;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-right: 1rem;
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
        }

        .card-description {
            color: #666;
            margin-bottom: 1.5rem;
            line-height: 1.5;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #5a6268;
            box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            margin-bottom: 1rem;
        }

        .status-online {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
        }

        .status-offline {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
        }

        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .quick-actions h3 {
            margin-bottom: 1rem;
            color: #333;
        }

        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
        }

        @media (max-width: 768px) {
            .navbar-content {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .container {
                padding: 1rem;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="navbar-content">
            <div class="navbar-brand">
                <i class="fas fa-bus"></i>
                BusTrack
            </div>
            <div class="navbar-user">
                <div class="nav-links">
                    <a href="{% url 'profile' %}"><i class="fas fa-user"></i> Profile</a>
                    <a href="{% url 'signout' %}"><i class="fas fa-sign-out-alt"></i> Logout</a>
                </div>
                <div class="user-info">
                    <div class="user-name">{{ user.get_full_name|default:user.username }}</div>
                    <div class="user-role">Student</div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="welcome-section">
            <h1 class="welcome-title">Welcome back, {{ user.first_name|default:user.username }}!</h1>
            <p class="welcome-subtitle">Track your bus, manage your account, and stay connected</p>
        </div>

        <div class="dashboard-grid">
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <div>
                        <h3 class="card-title">Payment Verification</h3>
                    </div>
                </div>
                {% if user.payment_receipts.exists %}
                    {% with latest_receipt=user.payment_receipts.first %}
                        {% if latest_receipt.status == 'approved' %}
                            <div class="status-indicator status-online">
                                <i class="fas fa-check-circle"></i>
                                Payment Verified
                            </div>
                            <p class="card-description">
                                Your payment has been verified! You can now access the bus service.
                            </p>
                        {% elif latest_receipt.status == 'pending' %}
                            <div class="status-indicator" style="background: rgba(255, 193, 7, 0.1); color: #ffc107;">
                                <i class="fas fa-clock"></i>
                                Verification Pending
                            </div>
                            <p class="card-description">
                                Your receipt is being reviewed by administrators. Please wait for verification.
                            </p>
                        {% else %}
                            <div class="status-indicator status-offline">
                                <i class="fas fa-times-circle"></i>
                                Payment Rejected
                            </div>
                            <p class="card-description">
                                Your receipt was rejected. Please submit a new receipt or contact support.
                            </p>
                        {% endif %}
                    {% endwith %}
                {% else %}
                    <p class="card-description">
                        Submit your payment receipt for verification to access the bus service.
                    </p>
                {% endif %}
                <div style="display: flex; gap: 10px; margin-top: 15px;">
                    <a href="{% url 'submit_receipt' %}" class="btn">
                        <i class="fas fa-upload"></i>
                        Submit Receipt
                    </a>
                    <a href="{% url 'receipt_status' %}" class="btn btn-secondary">
                        <i class="fas fa-list"></i>
                        View Status
                    </a>
                </div>
            </div>

            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div>
                        <h3 class="card-title">Bus Location</h3>
                    </div>
                </div>
                {% if driver_location %}
                    <div class="status-indicator status-online">
                        <i class="fas fa-circle"></i>
                        Bus is online
                    </div>
                {% else %}
                    <div class="status-indicator status-offline">
                        <i class="fas fa-circle"></i>
                        Bus location unavailable
                    </div>
                {% endif %}
                <p class="card-description">
                    Track your bus in real-time and get estimated arrival times for your pickup location.
                </p>
                <a href="{% url 'location_tracker' %}" class="btn">
                    <i class="fas fa-map"></i>
                    Track Bus
                </a>
            </div>
        </div>

        <div class="quick-actions">
            <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
            <div class="action-buttons">
                <a href="{% url 'profile' %}" class="btn btn-secondary">
                    <i class="fas fa-user-edit"></i>
                    Edit Profile
                </a>
                <a href="#" class="btn btn-secondary" onclick="shareLocation()">
                    <i class="fas fa-location-arrow"></i>
                    Share Location
                </a>
                <a href="#" class="btn btn-secondary">
                    <i class="fas fa-bell"></i>
                    Notifications
                </a>
            </div>
        </div>
    </div>

    <script>
        // Prevent browser back button issues
        window.addEventListener('load', function() {
            // Disable browser cache for this page
            if (typeof(Storage) !== "undefined") {
                if (sessionStorage.getItem('dashboardLoaded') === 'true') {
                    // If user tries to navigate back, redirect to dashboard
                    window.location.replace(window.location.href);
                }
                sessionStorage.setItem('dashboardLoaded', 'true');
            }

            // Handle browser navigation
            window.addEventListener('popstate', function(event) {
                // Prevent going back to login/signup pages
                window.location.replace('{% url "student_dashboard" %}');
            });
        });

        function shareLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    const latitude = position.coords.latitude;
                    const longitude = position.coords.longitude;

                    // Send location to server
                    fetch('{% url "update_location" %}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': '{{ csrf_token }}'
                        },
                        body: JSON.stringify({
                            latitude: latitude,
                            longitude: longitude
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            alert('Location shared successfully!');
                        } else {
                            alert('Failed to share location. Please try again.');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Failed to share location. Please try again.');
                    });
                }, function(error) {
                    alert('Unable to get your location. Please enable location services.');
                });
            } else {
                alert('Geolocation is not supported by this browser.');
            }
        }
    </script>
</body>
</html>